import fs from 'fs';
import path from 'path';

const modules = ['auth-service', 'core', 'hooks', 'types', 'utils'];

// Create re-export files for each module
modules.forEach(module => {
  const moduleDir = path.join('lib', module);
  
  // Create ES module re-export
  const esContent = `export * from './${module}/index.js';`;
  fs.writeFileSync(path.join('lib', `${module}.js`), esContent);
  
  // Create CommonJS re-export
  const cjsContent = `module.exports = require('./${module}/index.js');`;
  fs.writeFileSync(path.join('lib', `${module}.cjs`), cjsContent);
  
  // Create TypeScript declaration re-export
  const dtsContent = `export * from './${module}/index';`;
  fs.writeFileSync(path.join('lib', `${module}.d.ts`), dtsContent);
});

console.log('✅ Created submodule export files');
