import fs from 'fs';
import path from 'path';

const modules = [
  { name: 'auth-service', files: ['auth-service/index.js', 'auth-service/ucan-strategy.js'] },
  { name: 'core', files: ['core/methods.js'] },
  { name: 'hooks', files: ['hooks/index.js', 'hooks/ucan-auth.js', 'hooks/update-ucan.js'] },
  { name: 'types', files: ['types/index.js'] },
  { name: 'utils', files: ['utils/index.js', 'utils/check-exists.js'] }
];

// First, let's create the individual JS files by compiling the TypeScript modules
import { execSync } from 'child_process';

// Compile each module directory to JavaScript
modules.forEach(({ name }) => {
  const srcDir = `src/${name}`;
  const outDir = `lib/${name}`;

  try {
    // Compile TypeScript files to JavaScript for this module
    execSync(`npx tsc ${srcDir}/*.ts --outDir ${outDir} --module ESNext --target ESNext --moduleResolution node --declaration false --skipLibCheck`,
      { stdio: 'inherit' });
  } catch (error) {
    console.warn(`Warning: Could not compile ${name} module:`, error.message);
  }
});

// Create re-export files for each module
modules.forEach(({ name }) => {
  // Create ES module re-export
  const esContent = `export * from './${name}/index.js';`;
  fs.writeFileSync(path.join('lib', `${name}.js`), esContent);

  // Create CommonJS re-export
  const cjsContent = `module.exports = require('./${name}/index.js');`;
  fs.writeFileSync(path.join('lib', `${name}.cjs`), cjsContent);

  // Create TypeScript declaration re-export
  const dtsContent = `export * from './${name}/index';`;
  fs.writeFileSync(path.join('lib', `${name}.d.ts`), dtsContent);
});

console.log('✅ Created submodule export files');
