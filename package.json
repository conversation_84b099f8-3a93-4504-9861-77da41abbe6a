{"name": "feathers-ucan", "version": "0.1.1", "description": "Ucan extension of feathers jwt auth", "source": "src/index.ts", "unpkg": "lib/index.umd.js", "main": "lib/index.js", "module": "lib/index.module.js", "types": "lib/index.d.ts", "type": "module", "sideEffects": false, "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "default": "./lib/index.modern.js"}, "./auth-service": {"types": "./lib/auth-service.d.ts", "import": "./lib/auth-service.js", "require": "./lib/auth-service.cjs"}, "./core": {"types": "./lib/core.d.ts", "import": "./lib/core.js", "require": "./lib/core.cjs"}, "./hooks": {"types": "./lib/hooks.d.ts", "import": "./lib/hooks.js", "require": "./lib/hooks.cjs"}, "./types": {"types": "./lib/types.d.ts", "import": "./lib/types.js", "require": "./lib/types.cjs"}, "./utils": {"types": "./lib/utils.d.ts", "import": "./lib/utils.js", "require": "./lib/utils.cjs"}}, "scripts": {"test": "test", "prebuild": "rimraf lib dist && node scripts/gen-version.js", "build": "rm -rf lib && microbundle --tsconfig tsconfig.json && node scripts/create-submodule-exports.js", "dev": "microbundle --watch --tsconfig tsconfig.json --no-sourcemap", "dev:modules": "microbundle src/auth-service.ts src/core.ts src/hooks.ts src/types.ts src/utils.ts --watch --tsconfig tsconfig.json --no-sourcemap --no-pkg-main"}, "files": ["lib"], "repository": {"type": "git", "url": "git+https://gitlab.com/symbolsyntax/symbol-client.git"}, "author": "iy foundation", "license": "ISC", "bugs": {"url": "https://github.com/ha6755ad/symbol-utils/issues"}, "homepage": "https://github.com/ha6755ad/symbol-utils#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "microbundle": "^0.15.1", "prettier": "^2.5.1", "typescript": "^4.5.4"}, "dependencies": {"@feathersjs/authentication": "^5.0.11", "@ucans/ucans": "^0.12.0", "long-timeout": "^0.1.1", "radash": "^11.0.0", "symbol-ucan": "^0.0.6"}}