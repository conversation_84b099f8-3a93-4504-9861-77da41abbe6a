{"name": "feathers-ucan", "version": "0.1.0", "description": "Ucan extension of feathers jwt auth", "source": "src/index.ts", "unpkg": "lib/index.umd.js", "main": "lib/index.js", "module": "lib/index.module.js", "types": "lib/index.d.ts", "type": "module", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "default": "./lib/index.modern.js"}, "./auth-service": {"types": "./lib/auth-service.d.ts", "require": "./lib/auth-service.cjs", "default": "./lib/auth-service.js"}, "./core": {"types": "./lib/core.d.ts", "require": "./lib/core.cjs", "default": "./lib/core.js"}, "./hooks": {"types": "./lib/hooks.d.ts", "require": "./lib/hooks.cjs", "default": "./lib/hooks.js"}, "./types": {"types": "./lib/types.d.ts", "require": "./lib/types.cjs", "default": "./lib/types.js"}, "./utils": {"types": "./lib/utils.d.ts", "require": "./lib/utils.cjs", "default": "./lib/utils.js"}}, "scripts": {"test": "test", "prebuild": "rimraf lib dist && node scripts/gen-version.js", "build": "rm -rf lib && npm run build:main && npm run build:modules", "build:main": "microbundle --tsconfig tsconfig.json", "build:modules": "microbundle src/auth-service.ts src/core.ts src/hooks.ts src/types.ts src/utils.ts --tsconfig tsconfig.json --no-pkg-main", "dev": "microbundle --watch --tsconfig tsconfig.json --no-sourcemap"}, "files": ["lib"], "repository": {"type": "git", "url": "git+https://gitlab.com/symbolsyntax/symbol-client.git"}, "author": "iy foundation", "license": "ISC", "bugs": {"url": "https://github.com/ha6755ad/symbol-utils/issues"}, "homepage": "https://github.com/ha6755ad/symbol-utils#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "microbundle": "^0.15.1", "prettier": "^2.5.1", "typescript": "^4.5.4"}, "dependencies": {"@feathersjs/authentication": "^5.0.11", "@ucans/ucans": "^0.12.0", "long-timeout": "^0.1.1", "radash": "^11.0.0", "symbol-ucan": "^0.0.6"}}