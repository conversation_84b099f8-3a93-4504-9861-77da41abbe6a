{"compilerOptions": {"moduleResolution": "node", "target": "ESNext", "module": "ESNext", "lib": ["dom", "es2021", "webworker"], "strict": true, "sourceMap": true, "declaration": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "declarationDir": "lib", "outDir": "lib"}, "include": ["src/**/*"], "exclude": ["node_modules"], "ts-node": {"compilerOptions": {"target": "es2022", "module": "es2022"}}}